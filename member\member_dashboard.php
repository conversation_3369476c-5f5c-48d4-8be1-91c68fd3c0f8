<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isset($_SESSION['member_id'])) {
    header('Location: ../login.php');
    exit;
}

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member details
$query = "SELECT * FROM members WHERE id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$member = $stmt->fetch();

// Check if the member is signed in with Google
$is_google_user = !empty($member['google_id']);

// Get current loans
$query = "SELECT bl.*, b.title, b.author, b.isbn, b.cover_image
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status != 'returned'
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$current_loans = $stmt->fetchAll();

// Get loan history
$query = "SELECT bl.*, b.title, b.author, b.isbn
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status = 'returned'
          ORDER BY bl.return_date DESC
          LIMIT 5";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$loan_history = $stmt->fetchAll();

// Get reservations
$query = "SELECT br.*, b.title, b.author, b.isbn
          FROM book_reservations br
          JOIN books b ON br.book_id = b.id
          WHERE br.member_id = :member_id AND br.status IN ('pending', 'ready')
          ORDER BY br.reservation_date DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$reservations = $stmt->fetchAll();

// Calculate total fines
$query = "SELECT SUM(fine) as total_fine FROM book_loans
          WHERE member_id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$total_fine = $stmt->fetch()['total_fine'] ?? 0;

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 20px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: bold;
        }
        .navbar {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border: 1px solid #ddd;
        }
        .overdue {
            color: #dc3545;
            font-weight: bold;
        }
        /* Enhanced dashboard styles */
        .welcome-card {
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stats-card {
            text-align: center;
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stats-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #007bff;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border: none;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            font-weight: bold;
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="member_dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../catalog.php">Book Catalog</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i><?php echo h($_SESSION['member_name']); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                            <li><a class="dropdown-item" href="profile.php">My Profile</a></li>
                            <li><a class="dropdown-item" href="my_loans.php">My Loans</a></li>
                            <li><a class="dropdown-item" href="recommendations.php">Recommendations</a></li>
                            <?php if (!$is_google_user): ?>
                            <li><a class="dropdown-item" href="../google_account.php">
                                <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="16" height="16" class="me-2">
                                Link Google Account
                            </a></li>
                            <?php else: ?>
                            <li>
                                <div class="dropdown-item d-flex align-items-center">
                                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="16" height="16" class="me-2">
                                    <span class="text-muted">Signed in with Google</span>
                                </div>
                            </li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <?php
        // Log member dashboard access
        error_log("Member Dashboard: Member ID: " . $_SESSION['member_id'] . ", Name: " . $_SESSION['member_name']);

        // Check for streamlined Google sign-in
        if (isset($_SESSION['streamlined_google_signin']) && $_SESSION['streamlined_google_signin']) {
            // Clear the streamlined sign-in flag
            unset($_SESSION['streamlined_google_signin']);

            // Show a small notification for streamlined sign-in
            error_log("Member Dashboard: Streamlined Google sign-in completed");
            ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert" style="background-color: #f8f9fa; color: #3c4043; border-color: #dadce0;">
                <div class="d-flex align-items-center">
                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="18" height="18" class="me-2">
                    <span>You've been signed in with your Google account</span>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php
        }
        // Check for regular Google login
        elseif (isset($_SESSION['google_login']) && $_SESSION['google_login']) { ?>
            <?php if (isset($_SESSION['new_google_user']) && $_SESSION['new_google_user']) { ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-google me-2"></i>
                    <strong>Welcome!</strong> Your account has been created using your Google account information.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php
                error_log("Member Dashboard: New Google user welcome message shown");
                unset($_SESSION['new_google_user']);
                ?>
            <?php } else { ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-google me-2"></i>
                    <strong>Welcome back!</strong> You've been signed in with your Google account.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php error_log("Member Dashboard: Returning Google user welcome message shown"); ?>
            <?php } ?>
            <?php unset($_SESSION['google_login']); ?>
        <?php } ?>

        <?php if (isset($_GET['login'])) { ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Debug Info:</strong> Login timestamp: <?php echo h($_GET['login']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php } ?>

        <div class="row">
            <div class="col-md-12">
                <h2 class="mb-4">Member Dashboard</h2>

                <!-- Welcome Card -->
                <div class="welcome-card">
                    <div class="row">
                        <div class="col-md-8">
                            <h4>Welcome back, <?php echo h($member['first_name']); ?>!</h4>
                            <p class="text-muted">Member since <?php echo formatDate($member['membership_date']); ?></p>
                        </div>
                        <div class="col-md-4 text-end">
                            <?php if ($total_fine > 0): ?>
                                <p class="text-danger mb-2">
                                    <i class="bi bi-exclamation-circle"></i>
                                    Outstanding Fine: $<?php echo number_format($total_fine, 2); ?>
                                </p>
                            <?php else: ?>
                                <p class="text-success mb-2">
                                    <i class="bi bi-check-circle"></i> No outstanding fines
                                </p>
                            <?php endif; ?>
                            <a href="profile.php" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-pencil"></i> Edit Profile
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stats-card">
                            <div class="stats-icon">
                                <i class="bi bi-book"></i>
                            </div>
                            <h3><?php echo count($current_loans); ?></h3>
                            <p class="text-muted">Current Loans</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #6c757d;">
                                <i class="bi bi-bookmark"></i>
                            </div>
                            <h3><?php echo count($reservations); ?></h3>
                            <p class="text-muted">Reservations</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #28a745;">
                                <i class="bi bi-clock-history"></i>
                            </div>
                            <h3><?php echo count($loan_history); ?></h3>
                            <p class="text-muted">Recent Returns</p>
                        </div>
                    </div>
                </div>

                <!-- Member Info Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Member Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo h($member['email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo h($member['phone'] ?: 'Not provided'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Membership Date:</strong> <?php echo formatDate($member['membership_date']); ?></p>
                                <p><strong>Status:</strong>
                                    <span class="badge bg-success"><?php echo ucfirst($member['membership_status']); ?></span>
                                </p>
                                <p><strong>Outstanding Fines:</strong>
                                    <?php if ($total_fine > 0): ?>
                                        <span class="text-danger">$<?php echo number_format($total_fine, 2); ?></span>
                                    <?php else: ?>
                                        <span class="text-success">$0.00</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Loans -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Current Loans</h5>
                        <span class="badge bg-primary"><?php echo count($current_loans); ?></span>
                    </div>
                    <div class="card-body">
                        <?php if (count($current_loans) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Book</th>
                                            <th>Title</th>
                                            <th>Author</th>
                                            <th>Issue Date</th>
                                            <th>Due Date</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($current_loans as $loan): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($loan['cover_image']): ?>
                                                        <img src="../<?php echo h($loan['cover_image']); ?>" alt="Cover" class="book-cover">
                                                    <?php else: ?>
                                                        <div class="book-cover d-flex align-items-center justify-content-center bg-light">
                                                            <i class="bi bi-book fs-1 text-secondary"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo h($loan['title']); ?></td>
                                                <td><?php echo h($loan['author']); ?></td>
                                                <td><?php echo formatDate($loan['issue_date']); ?></td>
                                                <td class="<?php echo strtotime($loan['due_date']) < time() ? 'overdue' : ''; ?>">
                                                    <?php echo formatDate($loan['due_date']); ?>
                                                    <?php if (strtotime($loan['due_date']) < time()): ?>
                                                        <br><span class="badge bg-danger">Overdue</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($loan['status'] === 'borrowed'): ?>
                                                        <span class="badge bg-primary">Borrowed</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Overdue</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-center">You don't have any books checked out at the moment.</p>
                            <div class="text-center">
                                <a href="../catalog.php" class="btn btn-primary">Browse Books</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="../catalog.php" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-2"></i>Browse Books
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="my_loans.php" class="btn btn-info w-100">
                                    <i class="bi bi-book me-2"></i>My Loans
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="profile.php" class="btn btn-secondary w-100">
                                    <i class="bi bi-person me-2"></i>My Profile
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="recommendations.php" class="btn btn-success w-100">
                                    <i class="bi bi-star me-2"></i>Recommendations
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>